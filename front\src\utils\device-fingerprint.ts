/**
 * 設備指紋生成工具
 * 生成更準確的設備唯一標識，減少同一設備被識別為多個設備的問題
 */

// 擴展 Navigator 接口以包含非標準屬性
interface ExtendedNavigator extends Navigator {
  deviceMemory?: number;
  getBattery?: () => Promise<BatteryManager>;
  connection?: NetworkInformation;
  mozConnection?: NetworkInformation;
  webkitConnection?: NetworkInformation;
}

// 電池管理器接口
interface BatteryManager {
  level: number;
  charging: boolean;
  chargingTime: number;
  dischargingTime: number;
}

// 網絡信息接口
interface NetworkInformation {
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

// 擴展 Window 接口以包含 webkit 前綴的 AudioContext
interface ExtendedWindow extends Window {
  webkitAudioContext?: typeof AudioContext;
}

// 設備指紋組件類型
interface DeviceFingerprintComponents {
  screen: string;
  timezone: string;
  language: string;
  platform: string;
  hardwareConcurrency: number;
  deviceMemory?: number;
  colorDepth: number;
  pixelRatio: number;
  touchSupport: boolean;
  webgl: string;
  canvas: string;
  audio: string;
  fonts: string[];
  localStorage: boolean;
  sessionStorage: boolean;
  indexedDB: boolean;
  webRTC: boolean;
  battery?: string;
  connection?: string;
  // 允許字符串索引以支持動態訪問
  [key: string]: string | number | boolean | string[] | undefined;
}

export interface DeviceFingerprint {
  deviceId: string;
  components: DeviceFingerprintComponents;
  confidence: number; // 指紋可信度 (0-100)
}

/**
 * 生成設備指紋
 * @returns Promise<DeviceFingerprint>
 */
export async function generateDeviceFingerprint(): Promise<DeviceFingerprint> {
  const components = {
    screen: getScreenFingerprint(),
    timezone: getTimezoneFingerprint(),
    language: getLanguageFingerprint(),
    platform: getPlatformFingerprint(),
    hardwareConcurrency: getHardwareConcurrency(),
    deviceMemory: getDeviceMemory(),
    colorDepth: getColorDepth(),
    pixelRatio: getPixelRatio(),
    touchSupport: getTouchSupport(),
    webgl: getWebGLFingerprint(),
    canvas: getCanvasFingerprint(),
    audio: await getAudioFingerprint(),
    fonts: await getFontFingerprint(),
    localStorage: hasLocalStorage(),
    sessionStorage: hasSessionStorage(),
    indexedDB: hasIndexedDB(),
    webRTC: await hasWebRTC(),
    battery: await getBatteryFingerprint(),
    connection: getConnectionFingerprint(),
  };

  // 計算指紋可信度
  const confidence = calculateConfidence(components);

  // 生成設備ID
  const deviceId = await generateDeviceId(components);

  return {
    deviceId,
    components,
    confidence
  };
}

/**
 * 獲取屏幕指紋
 */
function getScreenFingerprint(): string {
  const screen = window.screen;
  return `${screen.width}x${screen.height}x${screen.availWidth}x${screen.availHeight}`;
}

/**
 * 獲取時區指紋
 */
function getTimezoneFingerprint(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * 獲取語言指紋
 */
function getLanguageFingerprint(): string {
  return `${navigator.language}|${navigator.languages.join(',')}`;
}

/**
 * 獲取平台指紋
 */
function getPlatformFingerprint(): string {
  return navigator.platform || 'unknown';
}

/**
 * 獲取硬件並發數
 */
function getHardwareConcurrency(): number {
  return navigator.hardwareConcurrency || 0;
}

/**
 * 獲取設備內存
 */
function getDeviceMemory(): number | undefined {
  return (navigator as ExtendedNavigator).deviceMemory;
}

/**
 * 獲取顏色深度
 */
function getColorDepth(): number {
  return screen.colorDepth || 0;
}

/**
 * 獲取像素比
 */
function getPixelRatio(): number {
  return window.devicePixelRatio || 1;
}

/**
 * 檢測觸摸支持
 */
function getTouchSupport(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * 獲取 WebGL 指紋
 */
function getWebGLFingerprint(): string {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

    if (!gl) return 'no-webgl';

    // 確保 gl 是 WebGLRenderingContext 類型
    const webglContext = gl as WebGLRenderingContext;

    // 安全地獲取 WebGL 參數
    const getWebGLParameter = (param: number): string => {
      try {
        const value = webglContext.getParameter(param);
        return value ? String(value) : 'unknown';
      } catch (e) {
        return 'error';
      }
    };

    const renderer = getWebGLParameter(webglContext.RENDERER);
    const vendor = getWebGLParameter(webglContext.VENDOR);
    const version = getWebGLParameter(webglContext.VERSION);
    const shadingLanguageVersion = getWebGLParameter(webglContext.SHADING_LANGUAGE_VERSION);

    // 獲取更多 WebGL 信息以增強指紋唯一性
    const maxTextureSize = getWebGLParameter(webglContext.MAX_TEXTURE_SIZE);
    const maxViewportDims = getWebGLParameter(webglContext.MAX_VIEWPORT_DIMS);
    const maxVertexAttribs = getWebGLParameter(webglContext.MAX_VERTEX_ATTRIBS);

    // 獲取支援的擴展
    const extensions = webglContext.getSupportedExtensions();
    const extensionList = extensions ? extensions.slice(0, 10).join(',') : 'no-extensions';

    return `${vendor}|${renderer}|${version}|${shadingLanguageVersion}|${maxTextureSize}|${maxViewportDims}|${maxVertexAttribs}|${extensionList}`;
  } catch (e) {
    return 'webgl-error';
  }
}

/**
 * 獲取 Canvas 指紋
 */
function getCanvasFingerprint(): string {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) return 'no-canvas';

    // 繪製一些圖形和文字
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillStyle = '#f60';
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = '#069';
    ctx.fillText('Device Fingerprint 🔒', 2, 15);
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
    ctx.fillText('Device Fingerprint 🔒', 4, 17);

    return canvas.toDataURL();
  } catch (e) {
    return 'canvas-error';
  }
}

/**
 * 獲取音頻指紋
 */
async function getAudioFingerprint(): Promise<string> {
  try {
    const audioContext = new (window.AudioContext || (window as ExtendedWindow).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const analyser = audioContext.createAnalyser();
    const gainNode = audioContext.createGain();
    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);

    oscillator.type = 'triangle';
    oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);

    gainNode.gain.setValueAtTime(0, audioContext.currentTime);

    oscillator.connect(analyser);
    analyser.connect(scriptProcessor);
    scriptProcessor.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.start(0);

    return new Promise((resolve) => {
      scriptProcessor.onaudioprocess = function(bins) {
        const array = new Float32Array(analyser.frequencyBinCount);
        analyser.getFloatFrequencyData(array);

        oscillator.stop();
        scriptProcessor.disconnect();
        audioContext.close();

        resolve(array.slice(0, 30).join(','));
      };
    });
  } catch (e) {
    return 'audio-error';
  }
}

/**
 * 獲取字體指紋
 */
async function getFontFingerprint(): Promise<string[]> {
  const testFonts = [
    'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
    'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
    'Trebuchet MS', 'Arial Black', 'Impact', 'Microsoft Sans Serif',
    'Tahoma', 'Lucida Console', 'Monaco', 'Menlo', 'Consolas'
  ];

  const availableFonts: string[] = [];

  for (const font of testFonts) {
    if (await isFontAvailable(font)) {
      availableFonts.push(font);
    }
  }

  return availableFonts;
}

/**
 * 檢測字體是否可用
 */
async function isFontAvailable(fontName: string): Promise<boolean> {
  try {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return false;

    const text = 'mmmmmmmmmmlli';
    const baselineSize = 72;

    context.font = `${baselineSize}px monospace`;
    const baselineWidth = context.measureText(text).width;

    context.font = `${baselineSize}px ${fontName}, monospace`;
    const testWidth = context.measureText(text).width;

    return baselineWidth !== testWidth;
  } catch (e) {
    return false;
  }
}

/**
 * 檢測存儲支持
 */
function hasLocalStorage(): boolean {
  try {
    return typeof localStorage !== 'undefined';
  } catch (e) {
    return false;
  }
}

function hasSessionStorage(): boolean {
  try {
    return typeof sessionStorage !== 'undefined';
  } catch (e) {
    return false;
  }
}

function hasIndexedDB(): boolean {
  try {
    return typeof indexedDB !== 'undefined';
  } catch (e) {
    return false;
  }
}

/**
 * 檢測 WebRTC 支持
 */
async function hasWebRTC(): Promise<boolean> {
  try {
    return typeof RTCPeerConnection !== 'undefined';
  } catch (e) {
    return false;
  }
}

/**
 * 獲取電池信息指紋
 */
async function getBatteryFingerprint(): Promise<string | undefined> {
  try {
    const extendedNavigator = navigator as ExtendedNavigator;
    if ('getBattery' in navigator && extendedNavigator.getBattery) {
      const battery = await extendedNavigator.getBattery();
      return `${battery.level}|${battery.charging}|${battery.chargingTime}|${battery.dischargingTime}`;
    }
  } catch (e) {
    // 忽略錯誤
  }
  return undefined;
}

/**
 * 獲取網絡連接信息
 */
function getConnectionFingerprint(): string | undefined {
  try {
    const extendedNavigator = navigator as ExtendedNavigator;
    const connection = extendedNavigator.connection || extendedNavigator.mozConnection || extendedNavigator.webkitConnection;
    if (connection) {
      return `${connection.effectiveType}|${connection.downlink}|${connection.rtt}`;
    }
  } catch (e) {
    // 忽略錯誤
  }
  return undefined;
}

/**
 * 計算指紋可信度
 */
function calculateConfidence(components: DeviceFingerprintComponents): number {
  let score = 0;
  let maxScore = 0;

  // 各個組件的權重
  const weights = {
    screen: 10,
    webgl: 15,
    canvas: 15,
    audio: 10,
    fonts: 10,
    platform: 5,
    hardwareConcurrency: 5,
    deviceMemory: 5,
    timezone: 5,
    language: 5,
    colorDepth: 3,
    pixelRatio: 3,
    touchSupport: 2,
    localStorage: 1,
    sessionStorage: 1,
    indexedDB: 1,
    webRTC: 1,
    battery: 2,
    connection: 1
  };

  for (const [key, weight] of Object.entries(weights)) {
    maxScore += weight;
    if (components[key] && components[key] !== 'error' && components[key] !== 'no-support') {
      score += weight;
    }
  }

  return Math.round((score / maxScore) * 100);
}

/**
 * 生成設備ID
 */
async function generateDeviceId(components: DeviceFingerprintComponents): Promise<string> {
  // 選擇最穩定的組件來生成ID
  const stableComponents = [
    components.screen,
    components.platform,
    components.hardwareConcurrency,
    components.colorDepth,
    components.pixelRatio,
    components.timezone,
    components.webgl,
    components.canvas.substring(0, 100), // 只取前100個字符
    components.fonts.slice(0, 10).join(','), // 只取前10個字體
  ].filter(Boolean);

  const fingerprint = stableComponents.join('|');

  // 使用 SubtleCrypto API 生成 SHA-256 哈希
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(fingerprint);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (e) {
    // 降級到簡單哈希
    return simpleHash(fingerprint);
  }
}

/**
 * 簡單哈希函數（降級方案）
 */
function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 轉換為32位整數
  }
  return Math.abs(hash).toString(16);
}

/**
 * 獲取簡化的設備指紋（用於快速識別）
 */
export function getSimpleDeviceFingerprint(): string {
  const components = [
    getScreenFingerprint(),
    getPlatformFingerprint(),
    getHardwareConcurrency(),
    getTimezoneFingerprint(),
    navigator.userAgent
  ];

  return simpleHash(components.join('|'));
}
